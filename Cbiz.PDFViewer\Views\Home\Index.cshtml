@{
    ViewData["Title"] = "PDF Viewer";
}

<div class="container-fluid">
    <div class="row">
        <!-- PDF Upload Section -->
        <div class="col-12 mb-4">
            <div class="upload-container">
                <form id="uploadForm" class="d-flex justify-content-center align-items-center">
                    <input type="file" id="pdfFile" accept=".pdf" class="form-control me-2" style="max-width: 300px;">
                    <button type="submit" class="btn btn-primary">Upload PDF</button>
                </form>
            </div>
        </div>

        <!-- PDF Viewer Section -->
        <div class="col-md-8">
            <div class="pdf-container">
                <embed id="pdfViewer" src="" type="application/pdf" width="100%" height="800px">
            </div>
        </div>

        <!-- File Information Section -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">File Information</h5>
                </div>
                <div class="card-body">
                    <form id="fileInfoForm" style="display: none;">
                        <div class="mb-3">
                            <label for="editFileName" class="form-label"><strong>File Name:</strong></label>
                            <input type="text" class="form-control" id="editFileName" placeholder="No file selected">
                        </div>
                        <div class="mb-3">
                            <label for="editFileSize" class="form-label"><strong>File Size:</strong></label>
                            <input type="text" class="form-control" id="editFileSize" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="editUploadDate" class="form-label"><strong>Upload Date:</strong></label>
                            <input type="text" class="form-control" id="editUploadDate" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="editContentType" class="form-label"><strong>Content Type:</strong></label>
                            <input type="text" class="form-control" id="editContentType" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="editDescription" class="form-label"><strong>Description:</strong></label>
                            <textarea class="form-control" id="editDescription" rows="3" placeholder="Add a description for this file..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editTags" class="form-label"><strong>Tags:</strong></label>
                            <input type="text" class="form-control" id="editTags" placeholder="Enter tags separated by commas">
                        </div>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-success">Save Information</button>
                            <button type="button" class="btn btn-secondary" id="cancelEdit">View Information</button>
                        </div>
                    </form>
                    <div class="file-info" id="fileInfoDisplay" style="display: none;">
                        <p><strong>File Name:</strong> <span id="fileName">No file selected</span></p>
                        <p><strong>File Size:</strong> <span id="fileSize">-</span></p>
                        <p><strong>Upload Date:</strong> <span id="uploadDate">-</span></p>
                        <p><strong>Content Type:</strong> <span id="contentType">-</span></p>
                        <p><strong>Description:</strong> <span id="fileDescription">-</span></p>
                        <p><strong>Tags:</strong> <span id="fileTags">-</span></p>
                        <div class="mt-3" id="editButtonContainer">
                            <button type="button" class="btn btn-primary btn-sm" id="editInfoBtn">Edit Information</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function (e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('pdfFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a PDF file');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/Home/UploadPDF', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // Update PDF viewer
                    document.getElementById('pdfViewer').src = result.data.filePath;

                    // Update file information display
                    document.getElementById('fileName').textContent = result.data.fileName;
                    document.getElementById('fileSize').textContent = formatFileSize(result.data.fileSize);
                    document.getElementById('uploadDate').textContent = new Date(result.data.uploadDate).toLocaleString();
                    document.getElementById('contentType').textContent = result.data.contentType;
                    document.getElementById('fileDescription').textContent = '-';
                    document.getElementById('fileTags').textContent = '-';

                    // Populate form fields with initial data
                    document.getElementById('editFileName').value = result.data.fileName;
                    document.getElementById('editFileSize').value = formatFileSize(result.data.fileSize);
                    document.getElementById('editUploadDate').value = new Date(result.data.uploadDate).toLocaleString();
                    document.getElementById('editContentType').value = result.data.contentType;
                    document.getElementById('editDescription').value = '';
                    document.getElementById('editTags').value = '';

                    // Show form by default (edit mode)
                    document.getElementById('fileInfoForm').style.display = 'block';
                    document.getElementById('fileInfoDisplay').style.display = 'none';
                } else {
                    alert(result.message || 'Error uploading file');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error uploading file');
            }
        });

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Edit Information Button Handler (switch from display to edit mode)
        document.getElementById('editInfoBtn').addEventListener('click', function() {
            document.getElementById('fileInfoDisplay').style.display = 'none';
            document.getElementById('fileInfoForm').style.display = 'block';
        });

        // Cancel Edit Button Handler (switch from edit to display mode)
        document.getElementById('cancelEdit').addEventListener('click', function() {
            document.getElementById('fileInfoForm').style.display = 'none';
            document.getElementById('fileInfoDisplay').style.display = 'block';
        });

        // File Information Form Submit Handler
        document.getElementById('fileInfoForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const fileInfoData = {
                fileName: document.getElementById('editFileName').value,
                description: document.getElementById('editDescription').value,
                tags: document.getElementById('editTags').value
            };

            try {
                const response = await fetch('/Home/UpdateFileInfo', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(fileInfoData)
                });

                const result = await response.json();

                if (result.success) {
                    // Update display with new information
                    document.getElementById('fileName').textContent = fileInfoData.fileName;
                    document.getElementById('fileDescription').textContent = fileInfoData.description || '-';
                    document.getElementById('fileTags').textContent = fileInfoData.tags || '-';

                    // Switch to display mode after successful save
                    document.getElementById('fileInfoForm').style.display = 'none';
                    document.getElementById('fileInfoDisplay').style.display = 'block';

                    alert('File information updated successfully!');
                } else {
                    alert(result.message || 'Error updating file information');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error updating file information');
            }
        });
    </script>
}

@section Styles {
    <style>
        .upload-container {
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .pdf-container {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            height: 800px;
        }

        .file-info p {
            margin-bottom: 0.5rem;
        }

        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        #fileInfoForm {
            transition: all 0.3s ease;
        }

        #fileInfoDisplay {
            transition: all 0.3s ease;
        }

        .form-label {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        #editButtonContainer {
            border-top: 1px solid #dee2e6;
            padding-top: 1rem;
        }

        .btn-sm {
            font-size: 0.875rem;
        }
    </style>
}
