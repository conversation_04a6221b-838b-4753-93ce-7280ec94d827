@{
    ViewData["Title"] = "PDF Viewer";
}

<div class="container-fluid">
    <div class="row">
        <!-- PDF Upload Section -->
        <div class="col-12 mb-4">
            <div class="upload-container">
                <form id="uploadForm" class="d-flex justify-content-center align-items-center">
                    <input type="file" id="pdfFile" accept=".pdf" class="form-control me-2" style="max-width: 300px;">
                    <button type="submit" class="btn btn-primary">Upload PDF</button>
                </form>
            </div>
        </div>

        <!-- PDF Viewer Section -->
        <div class="col-md-8">
            <div class="pdf-container">
                <embed id="pdfViewer" src="" type="application/pdf" width="100%" height="800px">
            </div>
        </div>

        <!-- File Information Section -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">File Information</h5>
                </div>
                <div class="card-body">
                    <form id="fileInfoForm" style="display: none;">
                        <div class="mb-3">
                            <label for="editFileName" class="form-label"><strong>File Name:</strong></label>
                            <input type="text" class="form-control" id="editFileName" placeholder="No file selected">
                        </div>
                        <div class="mb-3">
                            <label for="editFileSize" class="form-label"><strong>File Size:</strong></label>
                            <input type="text" class="form-control" id="editFileSize" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="editUploadDate" class="form-label"><strong>Upload Date:</strong></label>
                            <input type="text" class="form-control" id="editUploadDate" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="editContentType" class="form-label"><strong>Content Type:</strong></label>
                            <input type="text" class="form-control" id="editContentType" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="editDescription" class="form-label"><strong>Description:</strong></label>
                            <textarea class="form-control" id="editDescription" rows="3" placeholder="Add a description for this file..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editTags" class="form-label"><strong>Tags:</strong></label>
                            <input type="text" class="form-control" id="editTags" placeholder="Enter tags separated by commas">
                        </div>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-success">Save Information</button>
                            <button type="button" class="btn btn-secondary" id="cancelEdit">View Information</button>
                        </div>
                    </form>
                    <div class="file-info" id="fileInfoDisplay" style="display: none;">
                        <p><strong>File Name:</strong> <span id="fileName">No file selected</span></p>
                        <p><strong>File Size:</strong> <span id="fileSize">-</span></p>
                        <p><strong>Upload Date:</strong> <span id="uploadDate">-</span></p>
                        <p><strong>Content Type:</strong> <span id="contentType">-</span></p>
                        <p><strong>Description:</strong> <span id="fileDescription">-</span></p>
                        <p><strong>Tags:</strong> <span id="fileTags">-</span></p>
                        <div class="mt-3" id="editButtonContainer">
                            <button type="button" class="btn btn-primary btn-sm" id="editInfoBtn">Edit Information</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function (e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('pdfFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a PDF file');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/Home/UploadPDF', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // Update PDF viewer
                    document.getElementById('pdfViewer').src = result.data.filePath;

                    // Update file information display
                    document.getElementById('fileName').textContent = result.data.fileName;
                    document.getElementById('fileSize').textContent = formatFileSize(result.data.fileSize);
                    document.getElementById('uploadDate').textContent = new Date(result.data.uploadDate).toLocaleString();
                    document.getElementById('contentType').textContent = result.data.contentType;
                    document.getElementById('fileDescription').textContent = '-';
                    document.getElementById('fileTags').textContent = '-';

                    // Populate form fields with initial data
                    document.getElementById('editFileName').value = result.data.fileName;
                    document.getElementById('editFileSize').value = formatFileSize(result.data.fileSize);
                    document.getElementById('editUploadDate').value = new Date(result.data.uploadDate).toLocaleString();
                    document.getElementById('editContentType').value = result.data.contentType;
                    document.getElementById('editDescription').value = '';
                    document.getElementById('editTags').value = '';

                    // Show form by default (edit mode)
                    document.getElementById('fileInfoForm').style.display = 'block';
                    document.getElementById('fileInfoDisplay').style.display = 'none';
                } else {
                    alert(result.message || 'Error uploading file');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error uploading file');
            }
        });

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Edit Information Button Handler (switch from display to edit mode)
        document.getElementById('editInfoBtn').addEventListener('click', function() {
            document.getElementById('fileInfoDisplay').style.display = 'none';
            document.getElementById('fileInfoForm').style.display = 'block';
        });

        // Cancel Edit Button Handler (switch from edit to display mode)
        document.getElementById('cancelEdit').addEventListener('click', function() {
            document.getElementById('fileInfoForm').style.display = 'none';
            document.getElementById('fileInfoDisplay').style.display = 'block';
        });

        // File Information Form Submit Handler
        document.getElementById('fileInfoForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const fileInfoData = {
                fileName: document.getElementById('editFileName').value,
                description: document.getElementById('editDescription').value,
                tags: document.getElementById('editTags').value
            };

            try {
                const response = await fetch('/Home/UpdateFileInfo', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(fileInfoData)
                });

                const result = await response.json();

                if (result.success) {
                    // Update display with new information
                    document.getElementById('fileName').textContent = fileInfoData.fileName;
                    document.getElementById('fileDescription').textContent = fileInfoData.description || '-';
                    document.getElementById('fileTags').textContent = fileInfoData.tags || '-';

                    // Switch to display mode after successful save
                    document.getElementById('fileInfoForm').style.display = 'none';
                    document.getElementById('fileInfoDisplay').style.display = 'block';

                    alert('File information updated successfully!');
                } else {
                    alert(result.message || 'Error updating file information');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error updating file information');
            }
        });
    </script>
}

@section Styles {
    <style>
        /* Apply Inter font to the entire application */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 400;
            line-height: 1.6;
            color: #2d3748;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            color: #1a202c;
        }

        .navbar-brand {
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            font-size: 1.5rem;
            color: #2d3748 !important;
        }

        .upload-container {
            padding: 32px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            margin-bottom: 24px;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
            border: none;
        }

        .upload-container .form-control {
            border-radius: 12px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.9);
            font-family: 'Inter', sans-serif;
            font-weight: 500;
            padding: 12px 16px;
            transition: all 0.3s ease;
        }

        .upload-container .form-control:focus {
            border-color: rgba(255, 255, 255, 0.8);
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
            background: rgba(255, 255, 255, 1);
        }

        .upload-container .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 12px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .upload-container .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .pdf-container {
            background: #ffffff;
            padding: 20px;
            border-radius: 16px;
            height: 800px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
        }

        .pdf-container embed {
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .file-info p {
            margin-bottom: 0.75rem;
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            color: #4a5568;
        }

        .file-info strong {
            font-weight: 600;
            color: #2d3748;
        }

        .card {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-bottom: 1px solid #e2e8f0;
            padding: 20px 24px;
        }

        .card-header h5 {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .card-body {
            padding: 24px;
        }

        #fileInfoForm {
            transition: all 0.3s ease;
        }

        #fileInfoDisplay {
            transition: all 0.3s ease;
        }

        .form-label {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2d3748;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .form-control, .form-select {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            padding: 12px 16px;
            transition: all 0.3s ease;
            background: #ffffff;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            border-radius: 12px;
            padding: 12px 24px;
            transition: all 0.3s ease;
            border: none;
            text-transform: none;
            letter-spacing: 0.025em;
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(72, 187, 120, 0.4);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary:hover {
            background: #cbd5e0;
            color: #2d3748;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }

        #editButtonContainer {
            border-top: 1px solid #e2e8f0;
            padding-top: 1.5rem;
            margin-top: 1rem;
        }

        .btn-sm {
            font-size: 0.875rem;
            padding: 8px 16px;
        }

        /* Modern scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Smooth animations */
        * {
            transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* Modern focus styles */
        .form-control:focus,
        .btn:focus {
            outline: none;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .upload-container {
                padding: 20px;
                margin-bottom: 16px;
            }

            .card-body {
                padding: 16px;
            }

            .pdf-container {
                height: 600px;
                padding: 12px;
            }
        }
    </style>
}
