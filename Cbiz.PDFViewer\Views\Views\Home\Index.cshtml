@{
    ViewData["Title"] = "PDF Viewer";
}

<div class="container-fluid">
    <div class="row">
        <!-- PDF Upload Section -->
        <div class="col-12 mb-4">
            <div class="upload-container">
                <form id="uploadForm" class="d-flex justify-content-center align-items-center">
                    <input type="file" id="pdfFile" accept=".pdf" class="form-control me-2" style="max-width: 300px;">
                    <button type="submit" class="btn btn-primary">Upload PDF</button>
                </form>
            </div>
        </div>

        <!-- PDF Viewer Section -->
        <div class="col-md-8">
            <div class="pdf-container">
                <embed id="pdfViewer" src="" type="application/pdf" width="100%" height="800px">
            </div>
        </div>

        <!-- File Information Section -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">File Information</h5>
                </div>
                <div class="card-body">
                    <div class="file-info">
                        <p><strong>File Name:</strong> <span id="fileName">No file selected</span></p>
                        <p><strong>File Size:</strong> <span id="fileSize">-</span></p>
                        <p><strong>Upload Date:</strong> <span id="uploadDate">-</span></p>
                        <p><strong>Content Type:</strong> <span id="contentType">-</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function (e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('pdfFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a PDF file');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/Home/UploadPDF', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // Update PDF viewer
                    document.getElementById('pdfViewer').src = result.data.filePath;

                    // Update file information
                    document.getElementById('fileName').textContent = result.data.fileName;
                    document.getElementById('fileSize').textContent = formatFileSize(result.data.fileSize);
                    document.getElementById('uploadDate').textContent = new Date(result.data.uploadDate).toLocaleString();
                    document.getElementById('contentType').textContent = result.data.contentType;
                } else {
                    alert(result.message || 'Error uploading file');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error uploading file');
            }
        });

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
}

@section Styles {
    <style>
        .upload-container {
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .pdf-container {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            height: 800px;
        }

        .file-info p {
            margin-bottom: 0.5rem;
        }

        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
    </style>
}
