html {
  font-size: 14px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #667eea;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: #2d3748;
  background-color: #f8fafc;
}

/* Modern navbar styling */
.navbar {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95) !important;
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  color: #2d3748 !important;
}

.nav-link {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  color: #4a5568 !important;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #667eea !important;
}

/* Modern footer styling */
.footer {
  background-color: #2d3748;
  color: #a0aec0;
  font-family: 'Inter', sans-serif;
  font-weight: 400;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}