using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Cbiz.PDFViewer.Models;

namespace Cbiz.PDFViewer.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly IWebHostEnvironment _webHostEnvironment;

    public HomeController(ILogger<HomeController> logger, IWebHostEnvironment webHostEnvironment)
    {
        _logger = logger;
        _webHostEnvironment = webHostEnvironment;
    }

    public IActionResult Index()
    {
        return View();
    }

    [HttpPost]
    public async Task<IActionResult> UploadPDF(IFormFile file)
    {
        if (file == null || file.Length == 0)
            return Json(new { success = false, message = "No file uploaded" });

        if (!file.ContentType.Equals("application/pdf", StringComparison.OrdinalIgnoreCase))
            return Json(new { success = false, message = "Only PDF files are allowed" });

        try
        {
            var uploadsFolder = Path.Combine(_webHostEnvironment.WebRootPath, "uploads");
            if (!Directory.Exists(uploadsFolder))
                Directory.CreateDirectory(uploadsFolder);

            var uniqueFileName = Guid.NewGuid().ToString() + "_" + file.FileName;
            var filePath = Path.Combine(uploadsFolder, uniqueFileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            var pdfFile = new PDFFileModel
            {
                FileName = file.FileName,
                FileSize = file.Length,
                UploadDate = DateTime.UtcNow,
                FilePath = $"/uploads/{uniqueFileName}",
                ContentType = file.ContentType
            };

            return Json(new { success = true, data = pdfFile });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading PDF file");
            return Json(new { success = false, message = "Error uploading file" });
        }
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
