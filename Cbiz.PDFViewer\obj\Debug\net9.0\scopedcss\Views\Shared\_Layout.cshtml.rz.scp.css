/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-7awhe2s3zh] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-7awhe2s3zh] {
  color: #0077cc;
}

.btn-primary[b-7awhe2s3zh] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-7awhe2s3zh], .nav-pills .show > .nav-link[b-7awhe2s3zh] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-7awhe2s3zh] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-7awhe2s3zh] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-7awhe2s3zh] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-7awhe2s3zh] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-7awhe2s3zh] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
